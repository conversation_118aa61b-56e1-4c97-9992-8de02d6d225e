import os
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import re

def markdown_to_docx(md_file_path, docx_file_path):
    """将Markdown文件转换为DOCX文件"""
    
    # 读取markdown文件
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        with open(md_file_path, 'r', encoding='gbk') as f:
            content = f.read()
    
    # 创建Word文档
    doc = Document()
    
    # 处理markdown内容
    lines = content.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            # 空行
            i += 1
            continue
            
        # 处理标题
        if line.startswith('#'):
            level = 0
            while level < len(line) and line[level] == '#':
                level += 1
            
            title_text = line[level:].strip()
            
            # 添加标题
            if level == 1:
                heading = doc.add_heading(title_text, 1)
                heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
            elif level == 2:
                heading = doc.add_heading(title_text, 2)
            elif level == 3:
                heading = doc.add_heading(title_text, 3)
            elif level == 4:
                heading = doc.add_heading(title_text, 4)
            else:
                heading = doc.add_heading(title_text, min(level, 9))
                
        # 处理列表项
        elif line.startswith('- ') or line.startswith('* ') or re.match(r'^\d+\.', line):
            # 处理列表
            list_items = []
            while i < len(lines) and (lines[i].strip().startswith('- ') or 
                                     lines[i].strip().startswith('* ') or 
                                     re.match(r'^\d+\.', lines[i].strip())):
                item_line = lines[i].strip()
                if item_line.startswith('- ') or item_line.startswith('* '):
                    list_items.append(item_line[2:].strip())
                elif re.match(r'^\d+\.', item_line):
                    list_items.append(re.sub(r'^\d+\.\s*', '', item_line))
                i += 1
            
            # 添加列表项到文档
            for item in list_items:
                p = doc.add_paragraph()
                p.style = 'List Bullet'
                p.add_run(item)
            
            i -= 1  # 因为在while循环中已经递增了i
            
        # 处理普通段落
        else:
            # 收集连续的非标题行作为一个段落
            paragraph_lines = []
            while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('#'):
                paragraph_lines.append(lines[i].strip())
                i += 1
            
            if paragraph_lines:
                paragraph_text = ' '.join(paragraph_lines)
                
                # 处理引用格式 [数字]
                paragraph_text = re.sub(r'\[(\d+)\]', r'[\1]', paragraph_text)
                
                # 处理粗体文本 **文本**
                paragraph_text = re.sub(r'\*\*(.*?)\*\*', r'\1', paragraph_text)
                
                p = doc.add_paragraph(paragraph_text)
                p.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            
            i -= 1  # 因为在while循环中已经递增了i
        
        i += 1
    
    # 保存文档
    doc.save(docx_file_path)
    print(f"转换完成: {docx_file_path}")

if __name__ == "__main__":
    # 设置文件路径
    md_file = r"d:\BaiduSyncdisk\DEVsoftwares\projects for cursor\实验室 建设\碳足迹因子模拟校验与优化实验室申请报告_合并版.md"
    docx_file = r"d:\BaiduSyncdisk\DEVsoftwares\projects for cursor\实验室 建设\碳足迹因子模拟校验与优化实验室申请报告_合并版.docx"
    
    try:
        markdown_to_docx(md_file, docx_file)
        print("文件转换成功！")
    except Exception as e:
        print(f"转换失败: {e}")